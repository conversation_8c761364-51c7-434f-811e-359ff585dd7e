import { createApp } from 'vue'

console.log('🚀 开始启动最简化Vue应用...')

// 最简化的Vue组件
const SimpleApp = {
  template: `
    <div style="padding: 20px; text-align: center; background: #f0f0f0; min-height: 100vh;">
      <h1>🎯 最简化Vue应用测试</h1>
      <p>如果你看到这个页面，说明Vue基础功能正常</p>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
        更新时间
      </button>
    </div>
  `,
  data() {
    return {
      currentTime: new Date().toLocaleString('zh-CN')
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString('zh-CN')
      console.log('时间已更新:', this.currentTime)
    }
  },
  mounted() {
    console.log('✅ 最简化Vue组件已挂载')
  }
}

try {
  console.log('创建Vue应用实例...')
  const app = createApp(SimpleApp)
  
  console.log('挂载到#app元素...')
  app.mount('#app')
  
  console.log('✅ 最简化Vue应用启动成功!')
  
} catch (error) {
  console.error('❌ 最简化Vue应用启动失败:', error)
  
  // 显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; background: #ffebee; color: #c62828; text-align: center;">
        <h1>❌ Vue应用启动失败</h1>
        <p><strong>错误:</strong> ${error.message}</p>
        <button onclick="location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
      </div>
    `
  }
}
