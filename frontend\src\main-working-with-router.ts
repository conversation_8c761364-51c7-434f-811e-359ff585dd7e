import { createApp, ref, onMounted, h } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'

console.log('🚀 启动带路由的量化投资平台...')

// 创建简单的页面组件（使用渲染函数）
const Dashboard = {
  setup() {
    const accountData = ref({
      totalAssets: 1000000,
      availableFunds: 250000,
      todayPnL: 12500,
      totalPnL: 85000,
      positions: 8,
      strategies: 3
    })

    const marketData = ref([
      { name: '上证指数', value: 3245.67, change: 1.23, color: '#27ae60' },
      { name: '深证成指', value: 10876.54, change: -0.45, color: '#e74c3c' },
      { name: '创业板指', value: 2156.89, change: 0.78, color: '#27ae60' },
      { name: '科创50', value: 1023.45, change: 2.15, color: '#27ae60' }
    ])

    const recentTrades = ref([
      { symbol: '000001', name: '平安银行', type: '买入', quantity: 1000, price: 12.45, time: '09:30:15' },
      { symbol: '600036', name: '招商银行', type: '卖出', quantity: 500, price: 42.33, time: '10:15:22' },
      { symbol: '000002', name: '万科A', type: '买入', quantity: 800, price: 18.76, time: '11:20:08' }
    ])

    const formatNumber = (num) => {
      return new Intl.NumberFormat('zh-CN').format(num)
    }

    const formatPercent = (num) => {
      return (num >= 0 ? '+' : '') + num.toFixed(2) + '%'
    }

    return {
      accountData,
      marketData,
      recentTrades,
      formatNumber,
      formatPercent
    }
  },

  render() {
    return h('div', {
      style: {
        padding: '20px',
        maxWidth: '1400px',
        margin: '0 auto',
        background: '#f8f9fa',
        minHeight: 'calc(100vh - 80px)'
      }
    }, [
      // 页面标题
      h('div', {
        style: {
          marginBottom: '30px',
          textAlign: 'center'
        }
      }, [
        h('h1', {
          style: {
            color: '#2c3e50',
            marginBottom: '10px',
            fontSize: '32px',
            fontWeight: 'bold'
          }
        }, '📊 投资仪表板'),
        h('p', {
          style: {
            color: '#666',
            fontSize: '16px'
          }
        }, '实时监控您的投资组合和市场动态')
      ]),

      // 核心数据卡片
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }
      }, [
        // 总资产卡片
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          }
        }, [
          h('div', {
            style: {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '15px'
            }
          }, [
            h('h3', { style: { margin: '0', fontSize: '18px' } }, '💰 总资产'),
            h('span', { style: { fontSize: '24px' } }, '📈')
          ]),
          h('div', { style: { fontSize: '28px', fontWeight: 'bold', marginBottom: '10px' } },
            `¥${this.formatNumber(this.accountData.totalAssets)}`),
          h('div', { style: { fontSize: '14px', opacity: '0.9' } },
            `今日盈亏: ${this.accountData.todayPnL >= 0 ? '+' : ''}¥${this.formatNumber(this.accountData.todayPnL)}`)
        ]),

        // 可用资金卡片
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          }
        }, [
          h('div', {
            style: {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '15px'
            }
          }, [
            h('h3', { style: { margin: '0', fontSize: '18px' } }, '💳 可用资金'),
            h('span', { style: { fontSize: '24px' } }, '💵')
          ]),
          h('div', { style: { fontSize: '28px', fontWeight: 'bold', marginBottom: '10px' } },
            `¥${this.formatNumber(this.accountData.availableFunds)}`),
          h('div', { style: { fontSize: '14px', opacity: '0.9' } },
            `持仓数量: ${this.accountData.positions} 个`)
        ]),

        // 总盈亏卡片
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          }
        }, [
          h('div', {
            style: {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '15px'
            }
          }, [
            h('h3', { style: { margin: '0', fontSize: '18px' } }, '📊 总盈亏'),
            h('span', { style: { fontSize: '24px' } }, '🎯')
          ]),
          h('div', { style: { fontSize: '28px', fontWeight: 'bold', marginBottom: '10px' } },
            `${this.accountData.totalPnL >= 0 ? '+' : ''}¥${this.formatNumber(this.accountData.totalPnL)}`),
          h('div', { style: { fontSize: '14px', opacity: '0.9' } },
            `运行策略: ${this.accountData.strategies} 个`)
        ])
      ]),

      // 主要内容区域
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '30px',
          marginBottom: '30px'
        }
      }, [
        // 左侧：市场概览
        h('div', {
          style: {
            background: 'white',
            borderRadius: '15px',
            padding: '25px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', {
            style: {
              margin: '0 0 20px 0',
              color: '#2c3e50',
              fontSize: '20px',
              borderBottom: '2px solid #3498db',
              paddingBottom: '10px'
            }
          }, '📈 市场概览'),
          h('div', {
            style: {
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '15px'
            }
          }, this.marketData.map(item =>
            h('div', {
              style: {
                padding: '15px',
                border: '1px solid #eee',
                borderRadius: '10px',
                textAlign: 'center',
                transition: 'transform 0.2s ease',
                cursor: 'pointer'
              },
              onMouseover: (e) => e.target.style.transform = 'translateY(-2px)',
              onMouseout: (e) => e.target.style.transform = 'translateY(0)'
            }, [
              h('div', { style: { fontWeight: 'bold', marginBottom: '8px', color: '#2c3e50' } }, item.name),
              h('div', { style: { fontSize: '18px', fontWeight: 'bold', marginBottom: '5px' } }, item.value.toFixed(2)),
              h('div', {
                style: {
                  color: item.color,
                  fontWeight: 'bold',
                  fontSize: '14px'
                }
              }, this.formatPercent(item.change))
            ])
          ))
        ]),

        // 右侧：最近交易
        h('div', {
          style: {
            background: 'white',
            borderRadius: '15px',
            padding: '25px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', {
            style: {
              margin: '0 0 20px 0',
              color: '#2c3e50',
              fontSize: '20px',
              borderBottom: '2px solid #e74c3c',
              paddingBottom: '10px'
            }
          }, '🔄 最近交易'),
          h('div', {
            style: {
              maxHeight: '300px',
              overflowY: 'auto'
            }
          }, this.recentTrades.map(trade =>
            h('div', {
              style: {
                padding: '12px',
                border: '1px solid #f0f0f0',
                borderRadius: '8px',
                marginBottom: '10px',
                background: '#fafafa'
              }
            }, [
              h('div', {
                style: {
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '5px'
                }
              }, [
                h('span', { style: { fontWeight: 'bold', color: '#2c3e50' } }, `${trade.symbol} ${trade.name}`),
                h('span', {
                  style: {
                    color: trade.type === '买入' ? '#27ae60' : '#e74c3c',
                    fontWeight: 'bold',
                    fontSize: '12px'
                  }
                }, trade.type)
              ]),
              h('div', {
                style: {
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '14px',
                  color: '#666'
                }
              }, [
                h('span', `${trade.quantity}股 @ ¥${trade.price}`),
                h('span', trade.time)
              ])
            ])
          ))
        ])
      ])
    ])
  }
}

const Market = {
  setup() {
    const hotStocks = ref([
      { code: '000001', name: '平安银行', price: 12.45, change: 2.1, volume: '8.5亿', turnover: '105.2亿' },
      { code: '600036', name: '招商银行', price: 42.33, change: 1.8, volume: '3.2亿', turnover: '135.4亿' },
      { code: '000002', name: '万科A', price: 18.76, change: -1.2, volume: '5.8亿', turnover: '108.7亿' },
      { code: '600519', name: '贵州茅台', price: 1678.50, change: 0.8, volume: '0.8亿', turnover: '134.3亿' },
      { code: '000858', name: '五粮液', price: 128.90, change: -0.5, volume: '2.1亿', turnover: '27.1亿' },
      { code: '300750', name: '宁德时代', price: 198.45, change: 3.2, volume: '4.5亿', turnover: '89.3亿' }
    ])

    const indices = ref([
      { name: '上证指数', code: '000001', value: 3245.67, change: 1.23, volume: '2847亿' },
      { name: '深证成指', code: '399001', value: 10876.54, change: -0.45, volume: '3521亿' },
      { name: '创业板指', code: '399006', value: 2156.89, change: 0.78, volume: '1234亿' },
      { name: '科创50', code: '000688', value: 1023.45, change: 2.15, volume: '567亿' }
    ])

    const sectors = ref([
      { name: '银行', change: 1.8, leader: '招商银行', leaderChange: 1.8 },
      { name: '白酒', change: 0.3, leader: '贵州茅台', leaderChange: 0.8 },
      { name: '新能源', change: 2.5, leader: '宁德时代', leaderChange: 3.2 },
      { name: '房地产', change: -1.1, leader: '万科A', leaderChange: -1.2 },
      { name: '医药', change: 0.9, leader: '恒瑞医药', leaderChange: 1.5 },
      { name: '科技', change: 1.7, leader: '腾讯控股', leaderChange: 2.1 }
    ])

    const formatPercent = (num) => {
      return (num >= 0 ? '+' : '') + num.toFixed(2) + '%'
    }

    const getChangeColor = (change) => {
      return change >= 0 ? '#27ae60' : '#e74c3c'
    }

    return {
      hotStocks,
      indices,
      sectors,
      formatPercent,
      getChangeColor
    }
  },

  render() {
    return h('div', {
      style: {
        padding: '20px',
        maxWidth: '1400px',
        margin: '0 auto',
        background: '#f8f9fa',
        minHeight: 'calc(100vh - 80px)'
      }
    }, [
      // 页面标题
      h('div', {
        style: {
          marginBottom: '30px',
          textAlign: 'center'
        }
      }, [
        h('h1', {
          style: {
            color: '#2c3e50',
            marginBottom: '10px',
            fontSize: '32px',
            fontWeight: 'bold'
          }
        }, '📈 市场数据中心'),
        h('p', {
          style: {
            color: '#666',
            fontSize: '16px'
          }
        }, '实时股票行情、指数走势和板块动态')
      ]),

      // 主要指数
      h('div', {
        style: {
          background: 'white',
          borderRadius: '15px',
          padding: '25px',
          boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
          marginBottom: '30px'
        }
      }, [
        h('h3', {
          style: {
            margin: '0 0 20px 0',
            color: '#2c3e50',
            fontSize: '20px',
            borderBottom: '2px solid #3498db',
            paddingBottom: '10px'
          }
        }, '📊 主要指数'),
        h('div', {
          style: {
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px'
          }
        }, this.indices.map(index =>
          h('div', {
            style: {
              padding: '20px',
              border: '1px solid #eee',
              borderRadius: '12px',
              textAlign: 'center',
              background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            },
            onMouseover: (e) => {
              e.currentTarget.style.transform = 'translateY(-3px)'
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)'
            },
            onMouseout: (e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = 'none'
            }
          }, [
            h('div', { style: { fontWeight: 'bold', marginBottom: '10px', color: '#2c3e50', fontSize: '16px' } }, index.name),
            h('div', { style: { fontSize: '24px', fontWeight: 'bold', marginBottom: '8px', color: '#2c3e50' } }, index.value.toFixed(2)),
            h('div', {
              style: {
                color: this.getChangeColor(index.change),
                fontWeight: 'bold',
                fontSize: '16px',
                marginBottom: '8px'
              }
            }, this.formatPercent(index.change)),
            h('div', { style: { fontSize: '12px', color: '#666' } }, `成交量: ${index.volume}`)
          ])
        ))
      ]),

      // 内容区域
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '30px'
        }
      }, [
        // 左侧：热门股票
        h('div', {
          style: {
            background: 'white',
            borderRadius: '15px',
            padding: '25px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', {
            style: {
              margin: '0 0 20px 0',
              color: '#2c3e50',
              fontSize: '20px',
              borderBottom: '2px solid #e74c3c',
              paddingBottom: '10px'
            }
          }, '🔥 热门股票'),

          // 表格头部
          h('div', {
            style: {
              display: 'grid',
              gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr',
              gap: '10px',
              padding: '12px',
              background: '#f8f9fa',
              borderRadius: '8px',
              marginBottom: '10px',
              fontWeight: 'bold',
              fontSize: '14px',
              color: '#666'
            }
          }, [
            h('div', '股票'),
            h('div', { style: { textAlign: 'center' } }, '价格'),
            h('div', { style: { textAlign: 'center' } }, '涨跌幅'),
            h('div', { style: { textAlign: 'center' } }, '成交量'),
            h('div', { style: { textAlign: 'center' } }, '成交额')
          ]),

          // 股票列表
          h('div', {
            style: {
              maxHeight: '400px',
              overflowY: 'auto'
            }
          }, this.hotStocks.map(stock =>
            h('div', {
              style: {
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr',
                gap: '10px',
                padding: '15px 12px',
                borderBottom: '1px solid #f0f0f0',
                transition: 'background 0.2s ease',
                cursor: 'pointer'
              },
              onMouseover: (e) => e.currentTarget.style.background = '#f8f9fa',
              onMouseout: (e) => e.currentTarget.style.background = 'transparent'
            }, [
              h('div', [
                h('div', { style: { fontWeight: 'bold', color: '#2c3e50' } }, `${stock.code}`),
                h('div', { style: { fontSize: '12px', color: '#666' } }, stock.name)
              ]),
              h('div', {
                style: {
                  textAlign: 'center',
                  fontWeight: 'bold',
                  color: '#2c3e50'
                }
              }, `¥${stock.price.toFixed(2)}`),
              h('div', {
                style: {
                  textAlign: 'center',
                  color: this.getChangeColor(stock.change),
                  fontWeight: 'bold'
                }
              }, this.formatPercent(stock.change)),
              h('div', {
                style: {
                  textAlign: 'center',
                  fontSize: '12px',
                  color: '#666'
                }
              }, stock.volume),
              h('div', {
                style: {
                  textAlign: 'center',
                  fontSize: '12px',
                  color: '#666'
                }
              }, stock.turnover)
            ])
          ))
        ]),

        // 右侧：板块动态
        h('div', {
          style: {
            background: 'white',
            borderRadius: '15px',
            padding: '25px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', {
            style: {
              margin: '0 0 20px 0',
              color: '#2c3e50',
              fontSize: '20px',
              borderBottom: '2px solid #f39c12',
              paddingBottom: '10px'
            }
          }, '🏢 板块动态'),
          h('div', {
            style: {
              maxHeight: '400px',
              overflowY: 'auto'
            }
          }, this.sectors.map(sector =>
            h('div', {
              style: {
                padding: '15px',
                border: '1px solid #f0f0f0',
                borderRadius: '10px',
                marginBottom: '12px',
                background: '#fafafa',
                transition: 'all 0.2s ease',
                cursor: 'pointer'
              },
              onMouseover: (e) => {
                e.currentTarget.style.background = '#f0f0f0'
                e.currentTarget.style.transform = 'translateX(5px)'
              },
              onMouseout: (e) => {
                e.currentTarget.style.background = '#fafafa'
                e.currentTarget.style.transform = 'translateX(0)'
              }
            }, [
              h('div', {
                style: {
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '8px'
                }
              }, [
                h('span', { style: { fontWeight: 'bold', color: '#2c3e50' } }, sector.name),
                h('span', {
                  style: {
                    color: this.getChangeColor(sector.change),
                    fontWeight: 'bold'
                  }
                }, this.formatPercent(sector.change))
              ]),
              h('div', {
                style: {
                  fontSize: '12px',
                  color: '#666'
                }
              }, [
                h('span', '领涨: '),
                h('span', { style: { color: '#2c3e50', fontWeight: '500' } }, sector.leader),
                h('span', {
                  style: {
                    color: this.getChangeColor(sector.leaderChange),
                    marginLeft: '5px'
                  }
                }, this.formatPercent(sector.leaderChange))
              ])
            ])
          ))
        ])
      ])
    ])
  }
}

const Trading = {
  setup() {
    const orderForm = ref({
      stockCode: '',
      stockName: '',
      orderType: 'buy',
      priceType: 'market',
      price: 0,
      quantity: 100
    })

    const positions = ref([
      { code: '000001', name: '平安银行', quantity: 1000, avgPrice: 12.20, currentPrice: 12.45, pnl: 250, pnlPercent: 2.05 },
      { code: '600036', name: '招商银行', quantity: 500, avgPrice: 42.80, currentPrice: 42.33, pnl: -235, pnlPercent: -1.10 },
      { code: '000002', name: '万科A', quantity: 800, avgPrice: 19.10, currentPrice: 18.76, pnl: -272, pnlPercent: -1.78 }
    ])

    const orders = ref([
      { id: '001', code: '000001', name: '平安银行', type: '买入', quantity: 1000, price: 12.45, status: '已成交', time: '09:30:15' },
      { id: '002', code: '600036', name: '招商银行', type: '卖出', quantity: 500, price: 42.50, status: '部分成交', time: '10:15:22' },
      { id: '003', code: '000002', name: '万科A', type: '买入', quantity: 800, price: 18.80, status: '待成交', time: '11:20:08' }
    ])

    const accountInfo = ref({
      totalAssets: 1000000,
      availableFunds: 250000,
      marketValue: 750000,
      todayPnL: 12500
    })

    const submitOrder = () => {
      if (!orderForm.value.stockCode) {
        alert('请输入股票代码')
        return
      }
      console.log('📝 提交订单:', orderForm.value)
      alert('订单提交成功！')

      // 模拟添加到订单列表
      const newOrder = {
        id: String(orders.value.length + 1).padStart(3, '0'),
        code: orderForm.value.stockCode,
        name: orderForm.value.stockName || '未知股票',
        type: orderForm.value.orderType === 'buy' ? '买入' : '卖出',
        quantity: orderForm.value.quantity,
        price: orderForm.value.price,
        status: '待成交',
        time: new Date().toLocaleTimeString()
      }
      orders.value.unshift(newOrder)
    }

    const formatNumber = (num) => {
      return new Intl.NumberFormat('zh-CN').format(num)
    }

    const formatPercent = (num) => {
      return (num >= 0 ? '+' : '') + num.toFixed(2) + '%'
    }

    const getStatusColor = (status) => {
      switch (status) {
        case '已成交': return '#27ae60'
        case '部分成交': return '#f39c12'
        case '待成交': return '#3498db'
        default: return '#95a5a6'
      }
    }

    return {
      orderForm,
      positions,
      orders,
      accountInfo,
      submitOrder,
      formatNumber,
      formatPercent,
      getStatusColor
    }
  },

  render() {
    return h('div', {
      style: {
        padding: '20px',
        maxWidth: '1400px',
        margin: '0 auto',
        background: '#f8f9fa',
        minHeight: 'calc(100vh - 80px)'
      }
    }, [
      // 页面标题
      h('div', {
        style: {
          marginBottom: '30px',
          textAlign: 'center'
        }
      }, [
        h('h1', {
          style: {
            color: '#2c3e50',
            marginBottom: '10px',
            fontSize: '32px',
            fontWeight: 'bold'
          }
        }, '💼 交易中心'),
        h('p', {
          style: {
            color: '#666',
            fontSize: '16px'
          }
        }, '股票交易、持仓管理和订单跟踪')
      ]),

      // 账户信息卡片
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }
      }, [
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '总资产'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, `¥${this.formatNumber(this.accountInfo.totalAssets)}`)
        ]),
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '可用资金'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, `¥${this.formatNumber(this.accountInfo.availableFunds)}`)
        ]),
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '持仓市值'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, `¥${this.formatNumber(this.accountInfo.marketValue)}`)
        ]),
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '今日盈亏'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } },
            `${this.accountInfo.todayPnL >= 0 ? '+' : ''}¥${this.formatNumber(this.accountInfo.todayPnL)}`)
        ])
      ]),

      // 主要内容区域
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: '1fr 2fr',
          gap: '30px'
        }
      }, [
        // 左侧：下单区域
        h('div', {
          style: {
            background: 'white',
            borderRadius: '15px',
            padding: '25px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
            height: 'fit-content'
          }
        }, [
          h('h3', {
            style: {
              margin: '0 0 20px 0',
              color: '#2c3e50',
              fontSize: '20px',
              borderBottom: '2px solid #3498db',
              paddingBottom: '10px'
            }
          }, '📝 快速下单'),

          // 股票代码
          h('div', { style: { marginBottom: '20px' } }, [
            h('label', { style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' } }, '股票代码'),
            h('input', {
              type: 'text',
              placeholder: '请输入股票代码',
              value: this.orderForm.stockCode,
              style: {
                width: '100%',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '14px'
              },
              onInput: (e) => this.orderForm.stockCode = e.target.value
            })
          ]),

          // 交易类型和价格类型
          h('div', { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' } }, [
            h('div', [
              h('label', { style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' } }, '交易类型'),
              h('select', {
                value: this.orderForm.orderType,
                style: {
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '14px'
                },
                onChange: (e) => this.orderForm.orderType = e.target.value
              }, [
                h('option', { value: 'buy' }, '买入'),
                h('option', { value: 'sell' }, '卖出')
              ])
            ]),
            h('div', [
              h('label', { style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' } }, '价格类型'),
              h('select', {
                value: this.orderForm.priceType,
                style: {
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '14px'
                },
                onChange: (e) => this.orderForm.priceType = e.target.value
              }, [
                h('option', { value: 'market' }, '市价'),
                h('option', { value: 'limit' }, '限价')
              ])
            ])
          ]),

          // 价格和数量
          h('div', { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '25px' } }, [
            h('div', [
              h('label', { style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' } }, '价格'),
              h('input', {
                type: 'number',
                step: '0.01',
                placeholder: this.orderForm.priceType === 'market' ? '市价' : '请输入价格',
                disabled: this.orderForm.priceType === 'market',
                value: this.orderForm.price,
                style: {
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '14px',
                  background: this.orderForm.priceType === 'market' ? '#f8f9fa' : 'white'
                },
                onInput: (e) => this.orderForm.price = parseFloat(e.target.value)
              })
            ]),
            h('div', [
              h('label', { style: { display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' } }, '数量'),
              h('input', {
                type: 'number',
                step: '100',
                value: this.orderForm.quantity,
                style: {
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '14px'
                },
                onInput: (e) => this.orderForm.quantity = parseInt(e.target.value)
              })
            ])
          ]),

          // 提交按钮
          h('button', {
            onClick: this.submitOrder,
            style: {
              width: '100%',
              padding: '15px',
              background: this.orderForm.orderType === 'buy' ? '#e74c3c' : '#27ae60',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }
          }, `${this.orderForm.orderType === 'buy' ? '买入' : '卖出'} ${this.orderForm.stockCode || '股票'}`)
        ]),

        // 右侧：持仓和订单
        h('div', {
          style: {
            display: 'flex',
            flexDirection: 'column',
            gap: '20px'
          }
        }, [
          // 持仓信息
          h('div', {
            style: {
              background: 'white',
              borderRadius: '15px',
              padding: '25px',
              boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
            }
          }, [
            h('h3', {
              style: {
                margin: '0 0 20px 0',
                color: '#2c3e50',
                fontSize: '20px',
                borderBottom: '2px solid #e74c3c',
                paddingBottom: '10px'
              }
            }, '📊 持仓信息'),

            // 表格头部
            h('div', {
              style: {
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr',
                gap: '10px',
                padding: '12px',
                background: '#f8f9fa',
                borderRadius: '8px',
                marginBottom: '10px',
                fontWeight: 'bold',
                fontSize: '14px',
                color: '#666'
              }
            }, [
              h('div', '股票'),
              h('div', { style: { textAlign: 'center' } }, '数量'),
              h('div', { style: { textAlign: 'center' } }, '成本价'),
              h('div', { style: { textAlign: 'center' } }, '现价'),
              h('div', { style: { textAlign: 'center' } }, '盈亏')
            ]),

            // 持仓列表
            ...this.positions.map(position =>
              h('div', {
                style: {
                  display: 'grid',
                  gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr',
                  gap: '10px',
                  padding: '15px 12px',
                  borderBottom: '1px solid #f0f0f0',
                  transition: 'background 0.2s ease'
                },
                onMouseover: (e) => e.currentTarget.style.background = '#f8f9fa',
                onMouseout: (e) => e.currentTarget.style.background = 'transparent'
              }, [
                h('div', [
                  h('div', { style: { fontWeight: 'bold', color: '#2c3e50' } }, position.code),
                  h('div', { style: { fontSize: '12px', color: '#666' } }, position.name)
                ]),
                h('div', { style: { textAlign: 'center', color: '#2c3e50' } }, this.formatNumber(position.quantity)),
                h('div', { style: { textAlign: 'center', color: '#2c3e50' } }, `¥${position.avgPrice.toFixed(2)}`),
                h('div', { style: { textAlign: 'center', color: '#2c3e50' } }, `¥${position.currentPrice.toFixed(2)}`),
                h('div', { style: { textAlign: 'center' } }, [
                  h('div', {
                    style: {
                      color: position.pnl >= 0 ? '#27ae60' : '#e74c3c',
                      fontWeight: 'bold'
                    }
                  }, `${position.pnl >= 0 ? '+' : ''}¥${this.formatNumber(Math.abs(position.pnl))}`),
                  h('div', {
                    style: {
                      fontSize: '12px',
                      color: position.pnl >= 0 ? '#27ae60' : '#e74c3c'
                    }
                  }, this.formatPercent(position.pnlPercent))
                ])
              ])
            )
          ]),

          // 委托订单
          h('div', {
            style: {
              background: 'white',
              borderRadius: '15px',
              padding: '25px',
              boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
            }
          }, [
            h('h3', {
              style: {
                margin: '0 0 20px 0',
                color: '#2c3e50',
                fontSize: '20px',
                borderBottom: '2px solid #f39c12',
                paddingBottom: '10px'
              }
            }, '📋 委托订单'),

            // 表格头部
            h('div', {
              style: {
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr',
                gap: '10px',
                padding: '12px',
                background: '#f8f9fa',
                borderRadius: '8px',
                marginBottom: '10px',
                fontWeight: 'bold',
                fontSize: '14px',
                color: '#666'
              }
            }, [
              h('div', '股票'),
              h('div', { style: { textAlign: 'center' } }, '类型'),
              h('div', { style: { textAlign: 'center' } }, '数量'),
              h('div', { style: { textAlign: 'center' } }, '价格'),
              h('div', { style: { textAlign: 'center' } }, '状态'),
              h('div', { style: { textAlign: 'center' } }, '时间')
            ]),

            // 订单列表
            h('div', {
              style: {
                maxHeight: '300px',
                overflowY: 'auto'
              }
            }, this.orders.map(order =>
              h('div', {
                style: {
                  display: 'grid',
                  gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr',
                  gap: '10px',
                  padding: '12px',
                  borderBottom: '1px solid #f0f0f0',
                  transition: 'background 0.2s ease'
                },
                onMouseover: (e) => e.currentTarget.style.background = '#f8f9fa',
                onMouseout: (e) => e.currentTarget.style.background = 'transparent'
              }, [
                h('div', [
                  h('div', { style: { fontWeight: 'bold', color: '#2c3e50', fontSize: '12px' } }, order.code),
                  h('div', { style: { fontSize: '11px', color: '#666' } }, order.name)
                ]),
                h('div', {
                  style: {
                    textAlign: 'center',
                    color: order.type === '买入' ? '#e74c3c' : '#27ae60',
                    fontWeight: 'bold',
                    fontSize: '12px'
                  }
                }, order.type),
                h('div', { style: { textAlign: 'center', fontSize: '12px', color: '#2c3e50' } }, this.formatNumber(order.quantity)),
                h('div', { style: { textAlign: 'center', fontSize: '12px', color: '#2c3e50' } }, `¥${order.price.toFixed(2)}`),
                h('div', {
                  style: {
                    textAlign: 'center',
                    color: this.getStatusColor(order.status),
                    fontWeight: 'bold',
                    fontSize: '11px'
                  }
                }, order.status),
                h('div', { style: { textAlign: 'center', fontSize: '11px', color: '#666' } }, order.time)
              ])
            ))
          ])
        ])
      ])
    ])
  }
}

const Strategy = {
  setup() {
    const strategies = ref([
      {
        id: 1,
        name: '均线突破策略',
        type: 'MA_BREAKOUT',
        status: 'running',
        totalReturn: 8.5,
        dailyReturn: 0.3,
        maxDrawdown: -2.1,
        sharpeRatio: 1.85,
        winRate: 68.5,
        totalTrades: 156,
        capital: 500000,
        currentValue: 542500,
        startDate: '2024-01-15',
        description: '基于移动平均线的突破策略，适合趋势性行情'
      },
      {
        id: 2,
        name: '网格交易策略',
        type: 'GRID_TRADING',
        status: 'stopped',
        totalReturn: -2.1,
        dailyReturn: -0.1,
        maxDrawdown: -5.8,
        sharpeRatio: -0.45,
        winRate: 45.2,
        totalTrades: 89,
        capital: 300000,
        currentValue: 293700,
        startDate: '2024-02-01',
        description: '网格交易策略，适合震荡行情，低风险稳定收益'
      },
      {
        id: 3,
        name: 'RSI反转策略',
        type: 'RSI_REVERSAL',
        status: 'running',
        totalReturn: 12.3,
        dailyReturn: 0.5,
        maxDrawdown: -3.2,
        sharpeRatio: 2.15,
        winRate: 72.1,
        totalTrades: 203,
        capital: 800000,
        currentValue: 898400,
        startDate: '2023-12-01',
        description: 'RSI超买超卖反转策略，捕捉短期价格回调机会'
      },
      {
        id: 4,
        name: '动量因子策略',
        type: 'MOMENTUM',
        status: 'paused',
        totalReturn: 5.7,
        dailyReturn: 0.2,
        maxDrawdown: -4.1,
        sharpeRatio: 1.32,
        winRate: 58.9,
        totalTrades: 124,
        capital: 600000,
        currentValue: 634200,
        startDate: '2024-01-20',
        description: '基于动量因子的多因子选股策略'
      }
    ])

    const performanceData = ref([
      { date: '2024-01', return: 2.1 },
      { date: '2024-02', return: -0.8 },
      { date: '2024-03', return: 3.5 },
      { date: '2024-04', return: 1.9 },
      { date: '2024-05', return: 4.2 },
      { date: '2024-06', return: -1.2 },
      { date: '2024-07', return: 2.8 },
      { date: '2024-08', return: 1.5 }
    ])

    const toggleStrategy = (strategyId, action) => {
      const strategy = strategies.value.find(s => s.id === strategyId)
      if (strategy) {
        if (action === 'start') {
          strategy.status = 'running'
          alert(`策略 "${strategy.name}" 已启动`)
        } else if (action === 'stop') {
          strategy.status = 'stopped'
          alert(`策略 "${strategy.name}" 已停止`)
        } else if (action === 'pause') {
          strategy.status = 'paused'
          alert(`策略 "${strategy.name}" 已暂停`)
        }
      }
    }

    const getStatusColor = (status) => {
      switch (status) {
        case 'running': return '#27ae60'
        case 'stopped': return '#95a5a6'
        case 'paused': return '#f39c12'
        default: return '#95a5a6'
      }
    }

    const getStatusText = (status) => {
      switch (status) {
        case 'running': return '运行中'
        case 'stopped': return '已停止'
        case 'paused': return '已暂停'
        default: return '未知'
      }
    }

    const formatNumber = (num) => {
      return new Intl.NumberFormat('zh-CN').format(num)
    }

    const formatPercent = (num) => {
      return (num >= 0 ? '+' : '') + num.toFixed(2) + '%'
    }

    return {
      strategies,
      performanceData,
      toggleStrategy,
      getStatusColor,
      getStatusText,
      formatNumber,
      formatPercent
    }
  },

  render() {
    return h('div', {
      style: {
        padding: '20px',
        maxWidth: '1400px',
        margin: '0 auto',
        background: '#f8f9fa',
        minHeight: 'calc(100vh - 80px)'
      }
    }, [
      // 页面标题
      h('div', {
        style: {
          marginBottom: '30px',
          textAlign: 'center'
        }
      }, [
        h('h1', {
          style: {
            color: '#2c3e50',
            marginBottom: '10px',
            fontSize: '32px',
            fontWeight: 'bold'
          }
        }, '🤖 策略管理中心'),
        h('p', {
          style: {
            color: '#666',
            fontSize: '16px'
          }
        }, '量化策略开发、回测、部署和监控')
      ]),

      // 策略概览卡片
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }
      }, [
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '运行策略'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } },
            this.strategies.filter(s => s.status === 'running').length)
        ]),
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '总投入资金'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } },
            `¥${this.formatNumber(this.strategies.reduce((sum, s) => sum + s.capital, 0))}`)
        ]),
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '当前市值'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } },
            `¥${this.formatNumber(this.strategies.reduce((sum, s) => sum + s.currentValue, 0))}`)
        ]),
        h('div', {
          style: {
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            borderRadius: '12px',
            padding: '20px',
            color: 'white',
            textAlign: 'center'
          }
        }, [
          h('div', { style: { fontSize: '14px', marginBottom: '8px' } }, '总盈亏'),
          h('div', { style: { fontSize: '24px', fontWeight: 'bold' } },
            `¥${this.formatNumber(this.strategies.reduce((sum, s) => sum + (s.currentValue - s.capital), 0))}`)
        ])
      ]),

      // 策略列表
      h('div', {
        style: {
          background: 'white',
          borderRadius: '15px',
          padding: '25px',
          boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
          marginBottom: '30px'
        }
      }, [
        h('div', {
          style: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '25px'
          }
        }, [
          h('h3', {
            style: {
              margin: '0',
              color: '#2c3e50',
              fontSize: '20px'
            }
          }, '📊 策略列表'),
          h('button', {
            style: {
              padding: '10px 20px',
              background: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontWeight: 'bold'
            },
            onClick: () => alert('新建策略功能开发中...')
          }, '+ 新建策略')
        ]),

        h('div', {
          style: {
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '20px'
          }
        }, this.strategies.map(strategy =>
          h('div', {
            style: {
              border: '1px solid #eee',
              borderRadius: '12px',
              padding: '20px',
              background: '#fafafa',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            },
            onMouseover: (e) => {
              e.currentTarget.style.transform = 'translateY(-3px)'
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)'
            },
            onMouseout: (e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = 'none'
            }
          }, [
            // 策略头部
            h('div', {
              style: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '15px'
              }
            }, [
              h('h4', {
                style: {
                  margin: '0',
                  color: '#2c3e50',
                  fontSize: '18px',
                  fontWeight: 'bold'
                }
              }, strategy.name),
              h('span', {
                style: {
                  padding: '4px 12px',
                  borderRadius: '15px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  color: 'white',
                  background: this.getStatusColor(strategy.status)
                }
              }, this.getStatusText(strategy.status))
            ]),

            // 策略描述
            h('p', {
              style: {
                margin: '0 0 15px 0',
                color: '#666',
                fontSize: '14px',
                lineHeight: '1.4'
              }
            }, strategy.description),

            // 关键指标
            h('div', {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '15px',
                marginBottom: '20px'
              }
            }, [
              h('div', {
                style: {
                  textAlign: 'center',
                  padding: '10px',
                  background: 'white',
                  borderRadius: '8px'
                }
              }, [
                h('div', { style: { fontSize: '12px', color: '#666', marginBottom: '5px' } }, '总收益率'),
                h('div', {
                  style: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: strategy.totalReturn >= 0 ? '#27ae60' : '#e74c3c'
                  }
                }, this.formatPercent(strategy.totalReturn))
              ]),
              h('div', {
                style: {
                  textAlign: 'center',
                  padding: '10px',
                  background: 'white',
                  borderRadius: '8px'
                }
              }, [
                h('div', { style: { fontSize: '12px', color: '#666', marginBottom: '5px' } }, '夏普比率'),
                h('div', {
                  style: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#2c3e50'
                  }
                }, strategy.sharpeRatio.toFixed(2))
              ]),
              h('div', {
                style: {
                  textAlign: 'center',
                  padding: '10px',
                  background: 'white',
                  borderRadius: '8px'
                }
              }, [
                h('div', { style: { fontSize: '12px', color: '#666', marginBottom: '5px' } }, '胜率'),
                h('div', {
                  style: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#2c3e50'
                  }
                }, `${strategy.winRate.toFixed(1)}%`)
              ]),
              h('div', {
                style: {
                  textAlign: 'center',
                  padding: '10px',
                  background: 'white',
                  borderRadius: '8px'
                }
              }, [
                h('div', { style: { fontSize: '12px', color: '#666', marginBottom: '5px' } }, '最大回撤'),
                h('div', {
                  style: {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#e74c3c'
                  }
                }, this.formatPercent(strategy.maxDrawdown))
              ])
            ]),

            // 资金信息
            h('div', {
              style: {
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '20px',
                padding: '10px',
                background: 'white',
                borderRadius: '8px'
              }
            }, [
              h('div', [
                h('div', { style: { fontSize: '12px', color: '#666' } }, '投入资金'),
                h('div', { style: { fontWeight: 'bold', color: '#2c3e50' } }, `¥${this.formatNumber(strategy.capital)}`)
              ]),
              h('div', { style: { textAlign: 'right' } }, [
                h('div', { style: { fontSize: '12px', color: '#666' } }, '当前市值'),
                h('div', { style: { fontWeight: 'bold', color: '#2c3e50' } }, `¥${this.formatNumber(strategy.currentValue)}`)
              ])
            ]),

            // 操作按钮
            h('div', {
              style: {
                display: 'flex',
                gap: '10px'
              }
            }, [
              strategy.status === 'running' ?
                h('button', {
                  onClick: () => this.toggleStrategy(strategy.id, 'stop'),
                  style: {
                    flex: '1',
                    padding: '8px 16px',
                    background: '#e74c3c',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }
                }, '⏸️ 停止') :
                h('button', {
                  onClick: () => this.toggleStrategy(strategy.id, 'start'),
                  style: {
                    flex: '1',
                    padding: '8px 16px',
                    background: '#27ae60',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }
                }, '▶️ 启动'),

              h('button', {
                onClick: () => alert(`查看策略 "${strategy.name}" 详情`),
                style: {
                  flex: '1',
                  padding: '8px 16px',
                  background: '#3498db',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }
              }, '📊 详情'),

              h('button', {
                onClick: () => alert(`编辑策略 "${strategy.name}"`),
                style: {
                  flex: '1',
                  padding: '8px 16px',
                  background: '#f39c12',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }
              }, '✏️ 编辑')
            ])
          ])
        ))
      ])
    ])
  }
}

// 创建登录页面组件
const Login = {
  setup() {
    const loginForm = ref({
      username: '',
      password: ''
    })
    const isLogin = ref(true)
    const loading = ref(false)

    const handleLogin = async () => {
      if (!loginForm.value.username || !loginForm.value.password) {
        alert('请填写用户名和密码')
        return
      }

      loading.value = true
      try {
        // 模拟登录API调用
        const response = await fetch('http://localhost:8000/api/v1/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(loginForm.value)
        })

        if (response.ok) {
          const data = await response.json()
          console.log('登录成功:', data)

          // 保存token到localStorage
          localStorage.setItem('token', data.token)
          localStorage.setItem('userInfo', JSON.stringify(data.user))

          alert('登录成功！')
          // 跳转到仪表板
          router.push({ name: 'dashboard' })
        } else {
          const error = await response.json()
          alert('登录失败: ' + (error.detail || '未知错误'))
        }
      } catch (error) {
        console.error('登录错误:', error)
        alert('登录失败: 网络错误')
      } finally {
        loading.value = false
      }
    }

    const handleDemoLogin = () => {
      loginForm.value.username = 'admin'
      loginForm.value.password = 'admin123'
      handleLogin()
    }

    const toggleMode = () => {
      isLogin.value = !isLogin.value
    }

    return {
      loginForm,
      isLogin,
      loading,
      handleLogin,
      handleDemoLogin,
      toggleMode
    }
  },

  render() {
    return h('div', {
      style: {
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }
    }, [
      h('div', {
        style: {
          background: 'white',
          borderRadius: '16px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
          width: '100%',
          maxWidth: '400px',
          padding: '40px'
        }
      }, [
        // 标题
        h('div', {
          style: {
            textAlign: 'center',
            marginBottom: '30px'
          }
        }, [
          h('h1', {
            style: {
              color: '#2c3e50',
              marginBottom: '10px',
              fontSize: '28px'
            }
          }, '🚀 量化投资平台'),
          h('p', {
            style: {
              color: '#666',
              fontSize: '14px'
            }
          }, this.isLogin ? '登录您的账户' : '创建新账户')
        ]),

        // 表单
        h('form', {
          onSubmit: (e) => {
            e.preventDefault()
            this.handleLogin()
          }
        }, [
          // 用户名输入
          h('div', {
            style: { marginBottom: '20px' }
          }, [
            h('label', {
              style: {
                display: 'block',
                marginBottom: '8px',
                fontWeight: '500',
                color: '#2c3e50'
              }
            }, '用户名'),
            h('input', {
              type: 'text',
              placeholder: '请输入用户名',
              value: this.loginForm.username,
              onInput: (e) => this.loginForm.username = e.target.value,
              style: {
                width: '100%',
                padding: '12px 16px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.3s ease'
              }
            })
          ]),

          // 密码输入
          h('div', {
            style: { marginBottom: '20px' }
          }, [
            h('label', {
              style: {
                display: 'block',
                marginBottom: '8px',
                fontWeight: '500',
                color: '#2c3e50'
              }
            }, '密码'),
            h('input', {
              type: 'password',
              placeholder: '请输入密码',
              value: this.loginForm.password,
              onInput: (e) => this.loginForm.password = e.target.value,
              style: {
                width: '100%',
                padding: '12px 16px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.3s ease'
              }
            })
          ]),

          // 登录按钮
          h('button', {
            type: 'submit',
            disabled: this.loading,
            style: {
              width: '100%',
              padding: '12px',
              background: this.loading ? '#95a5a6' : '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: this.loading ? 'not-allowed' : 'pointer',
              marginBottom: '15px',
              transition: 'background 0.3s ease'
            }
          }, this.loading ? '登录中...' : (this.isLogin ? '登录' : '注册')),

          // 演示登录按钮
          this.isLogin ? h('button', {
            type: 'button',
            onClick: this.handleDemoLogin,
            disabled: this.loading,
            style: {
              width: '100%',
              padding: '12px',
              background: this.loading ? '#95a5a6' : '#27ae60',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              cursor: this.loading ? 'not-allowed' : 'pointer',
              marginBottom: '20px',
              transition: 'background 0.3s ease'
            }
          }, '🎯 演示登录 (admin/admin123)') : null,

          // 切换模式
          h('div', {
            style: {
              textAlign: 'center',
              fontSize: '14px',
              color: '#666'
            }
          }, [
            h('span', this.isLogin ? '还没有账户？' : '已有账户？'),
            h('button', {
              type: 'button',
              onClick: this.toggleMode,
              style: {
                background: 'none',
                border: 'none',
                color: '#3498db',
                cursor: 'pointer',
                textDecoration: 'underline',
                marginLeft: '5px'
              }
            }, this.isLogin ? '立即注册' : '立即登录')
          ])
        ])
      ])
    ])
  }
}

// 创建路由配置
const routes = [
  { path: '/login', component: Login, name: 'login', meta: { requiresAuth: false } },
  { path: '/', component: Dashboard, name: 'dashboard' },
  { path: '/market', component: Market, name: 'market' },
  { path: '/trading', component: Trading, name: 'trading' },
  { path: '/strategy', component: Strategy, name: 'strategy' }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 主应用组件 - 包含导航和路由视图
const MainApp = {
  setup() {
    const currentTime = ref('')
    const backendStatus = ref(null)
    const currentRoute = ref('dashboard')
    const isLoggedIn = ref(false)
    const userInfo = ref(null)
    const route = router.currentRoute

    const updateTime = () => {
      currentTime.value = new Date().toLocaleString('zh-CN')
    }

    const testBackend = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/health')
        if (response.ok) {
          const data = await response.json()
          backendStatus.value = {
            success: true,
            message: `连接正常 - ${data.status}`
          }
        } else {
          backendStatus.value = {
            success: false,
            message: `连接失败 - ${response.status}`
          }
        }
      } catch (error) {
        backendStatus.value = {
          success: false,
          message: `连接错误 - ${error.message}`
        }
      }
    }

    const checkLoginStatus = () => {
      const token = localStorage.getItem('token')
      const storedUserInfo = localStorage.getItem('userInfo')

      if (token && storedUserInfo) {
        try {
          userInfo.value = JSON.parse(storedUserInfo)
          isLoggedIn.value = true
        } catch (error) {
          console.error('解析用户信息失败:', error)
          logout()
        }
      }
    }

    const navigateTo = (routeName: string) => {
      // 检查是否需要登录
      if (!isLoggedIn.value && routeName !== 'login') {
        router.push({ name: 'login' })
        return
      }

      currentRoute.value = routeName
      router.push({ name: routeName })
    }

    const logout = () => {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      isLoggedIn.value = false
      userInfo.value = null
      router.push({ name: 'login' })
    }

    const goToLogin = () => {
      router.push({ name: 'login' })
    }

    onMounted(() => {
      updateTime()
      setInterval(updateTime, 1000)
      testBackend()
      checkLoginStatus()

      // 监听路由变化
      router.afterEach((to) => {
        currentRoute.value = to.name
      })

      console.log('✅ 带路由的量化投资平台已挂载')
    })

    return {
      currentTime,
      backendStatus,
      currentRoute,
      isLoggedIn,
      userInfo,
      route,
      testBackend,
      navigateTo,
      logout,
      goToLogin
    }
  },

  render() {
    // 如果是登录页面，直接显示路由内容，不显示导航
    if (this.route.value?.name === 'login') {
      return h('router-view')
    }

    return h('div', {
      style: {
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }
    }, [
      // 顶部导航栏
      h('nav', {
        style: {
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          padding: '15px 0',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          position: 'sticky',
          top: '0',
          zIndex: '1000'
        }
      }, [
        h('div', {
          style: {
            maxWidth: '1200px',
            margin: '0 auto',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '0 20px'
          }
        }, [
          // Logo和标题
          h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '15px'
            }
          }, [
            h('h1', {
              style: {
                color: '#2c3e50',
                margin: '0',
                fontSize: '24px'
              }
            }, '🚀 量化投资平台'),
            h('span', {
              style: {
                color: '#666',
                fontSize: '14px'
              }
            }, this.currentTime)
          ]),

          // 导航菜单 - 只在登录时显示
          this.isLoggedIn ? h('div', {
            style: {
              display: 'flex',
              gap: '20px'
            }
          }, [
            h('button', {
              onClick: () => this.navigateTo('dashboard'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'dashboard' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'dashboard' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '📊 仪表板'),
            h('button', {
              onClick: () => this.navigateTo('market'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'market' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'market' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '📈 市场数据'),
            h('button', {
              onClick: () => this.navigateTo('trading'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'trading' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'trading' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '💼 交易中心'),
            h('button', {
              onClick: () => this.navigateTo('strategy'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'strategy' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'strategy' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '🤖 策略管理')
          ]) : h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '15px'
            }
          }, [
            h('span', {
              style: {
                color: '#666',
                fontSize: '14px'
              }
            }, '请登录以访问完整功能'),
            h('button', {
              onClick: this.goToLogin,
              style: {
                padding: '8px 16px',
                background: '#3498db',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }
            }, '登录')
          ]),

          // 状态信息和用户信息
          h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '15px'
            }
          }, [
            // 后端连接状态
            this.backendStatus ? h('div', {
              style: {
                padding: '5px 10px',
                borderRadius: '15px',
                fontSize: '12px',
                fontWeight: 'bold',
                background: this.backendStatus.success ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
                color: this.backendStatus.success ? '#4CAF50' : '#f44336'
              }
            }, this.backendStatus.success ? '🟢 在线' : '🔴 离线') : null,

            // 用户信息 - 只在登录时显示
            this.isLoggedIn && this.userInfo ? h('div', {
              style: {
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '5px 10px',
                background: 'rgba(52, 152, 219, 0.1)',
                borderRadius: '15px'
              }
            }, [
              h('span', {
                style: {
                  fontSize: '12px',
                  color: '#3498db',
                  fontWeight: 'bold'
                }
              }, `👤 ${this.userInfo.username}`)
            ]) : null,

            // 操作按钮
            this.isLoggedIn ? h('button', {
              onClick: this.logout,
              style: {
                padding: '8px 15px',
                border: '1px solid #e74c3c',
                borderRadius: '6px',
                background: 'white',
                color: '#e74c3c',
                cursor: 'pointer',
                fontSize: '12px'
              }
            }, '🚪 登出') : null,

            h('button', {
              onClick: this.testBackend,
              style: {
                padding: '8px 15px',
                border: '1px solid #ddd',
                borderRadius: '6px',
                background: 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }
            }, '🔄 刷新')
          ])
        ])
      ]),

      // 主内容区域
      h('main', {
        style: {
          minHeight: 'calc(100vh - 80px)',
          background: '#f8f9fa'
        }
      }, [
        // 路由视图
        h('router-view')
      ])
    ])
  }
}

// 创建应用
const pinia = createPinia()
const app = createApp(MainApp)

// 配置应用
app.use(pinia)
app.use(router)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue错误:', err, info)
}

// 挂载应用
app.mount('#app')

console.log('🔧 带路由的量化投资平台已启动')
