import { createApp, ref, onMounted, h } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'

console.log('🚀 启动带路由的量化投资平台...')

// 创建简单的页面组件（使用渲染函数）
const Dashboard = {
  setup() {
    return () => h('div', {
      style: {
        padding: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }
    }, [
      h('h1', { style: { color: '#2c3e50', textAlign: 'center', marginBottom: '30px' } }, '📊 仪表板'),
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '20px'
        }
      }, [
        h('div', {
          style: {
            background: 'white',
            borderRadius: '10px',
            padding: '20px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', '💰 账户总览'),
          h('p', '总资产: ¥1,000,000'),
          h('p', '可用资金: ¥250,000'),
          h('p', { style: { color: '#27ae60' } }, '今日盈亏: +¥12,500')
        ]),
        h('div', {
          style: {
            background: 'white',
            borderRadius: '10px',
            padding: '20px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', '📈 市场概览'),
          h('p', '上证指数: 3,245.67 (+1.23%)'),
          h('p', '深证成指: 10,876.54 (-0.45%)'),
          h('p', '创业板指: 2,156.89 (+0.78%)')
        ])
      ])
    ])
  }
}

const Market = {
  setup() {
    return () => h('div', {
      style: {
        padding: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }
    }, [
      h('h1', { style: { color: '#2c3e50', textAlign: 'center', marginBottom: '30px' } }, '📈 市场数据'),
      h('div', {
        style: {
          background: 'white',
          borderRadius: '10px',
          padding: '20px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }
      }, [
        h('h3', '热门股票'),
        h('div', { style: { marginTop: '15px' } }, [
          h('div', { style: { display: 'flex', justifyContent: 'space-between', padding: '10px 0', borderBottom: '1px solid #eee' } }, [
            h('span', '000001 平安银行'),
            h('span', '¥12.45'),
            h('span', { style: { color: '#27ae60' } }, '+2.1%')
          ]),
          h('div', { style: { display: 'flex', justifyContent: 'space-between', padding: '10px 0', borderBottom: '1px solid #eee' } }, [
            h('span', '600036 招商银行'),
            h('span', '¥42.33'),
            h('span', { style: { color: '#27ae60' } }, '+1.8%')
          ]),
          h('div', { style: { display: 'flex', justifyContent: 'space-between', padding: '10px 0' } }, [
            h('span', '000002 万科A'),
            h('span', '¥18.76'),
            h('span', { style: { color: '#e74c3c' } }, '-1.2%')
          ])
        ])
      ])
    ])
  }
}

const Trading = {
  setup() {
    const orderForm = ref({
      stockCode: '',
      orderType: 'buy',
      price: 0,
      quantity: 100
    })

    const submitOrder = () => {
      console.log('📝 提交订单:', orderForm.value)
      alert('订单提交成功！')
    }

    return () => h('div', {
      style: {
        padding: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }
    }, [
      h('h1', { style: { color: '#2c3e50', textAlign: 'center', marginBottom: '30px' } }, '💼 交易中心'),
      h('div', {
        style: {
          background: 'white',
          borderRadius: '10px',
          padding: '20px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          maxWidth: '500px'
        }
      }, [
        h('h3', '📝 下单交易'),
        h('div', { style: { marginTop: '15px' } }, [
          h('div', { style: { marginBottom: '15px' } }, [
            h('label', { style: { display: 'block', marginBottom: '5px', fontWeight: '500' } }, '股票代码'),
            h('input', {
              type: 'text',
              placeholder: '请输入股票代码',
              style: {
                width: '100%',
                padding: '10px',
                border: '1px solid #ddd',
                borderRadius: '5px'
              },
              onInput: (e: any) => orderForm.value.stockCode = e.target.value
            })
          ]),
          h('div', { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' } }, [
            h('div', [
              h('label', { style: { display: 'block', marginBottom: '5px', fontWeight: '500' } }, '交易类型'),
              h('select', {
                style: {
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '5px'
                },
                onChange: (e: any) => orderForm.value.orderType = e.target.value
              }, [
                h('option', { value: 'buy' }, '买入'),
                h('option', { value: 'sell' }, '卖出')
              ])
            ]),
            h('div', [
              h('label', { style: { display: 'block', marginBottom: '5px', fontWeight: '500' } }, '交易数量'),
              h('input', {
                type: 'number',
                step: '100',
                value: orderForm.value.quantity,
                style: {
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '5px'
                },
                onInput: (e: any) => orderForm.value.quantity = parseInt(e.target.value)
              })
            ])
          ]),
          h('button', {
            onClick: submitOrder,
            style: {
              padding: '12px 30px',
              background: orderForm.value.orderType === 'buy' ? '#e74c3c' : '#27ae60',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontWeight: 'bold',
              cursor: 'pointer'
            }
          }, orderForm.value.orderType === 'buy' ? '买入' : '卖出')
        ])
      ])
    ])
  }
}

const Strategy = {
  setup() {
    return () => h('div', {
      style: {
        padding: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }
    }, [
      h('h1', { style: { color: '#2c3e50', textAlign: 'center', marginBottom: '30px' } }, '🤖 策略管理'),
      h('div', {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: '20px'
        }
      }, [
        h('div', {
          style: {
            background: 'white',
            borderRadius: '10px',
            padding: '20px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', '均线突破策略'),
          h('div', { style: { display: 'flex', justifyContent: 'space-between', margin: '10px 0' } }, [
            h('span', '状态:'),
            h('span', { style: { color: '#27ae60', fontWeight: 'bold' } }, '运行中')
          ]),
          h('div', { style: { display: 'flex', justifyContent: 'space-between', margin: '10px 0' } }, [
            h('span', '总收益率:'),
            h('span', { style: { color: '#27ae60' } }, '+8.5%')
          ]),
          h('button', {
            style: {
              padding: '8px 16px',
              background: '#e74c3c',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }
          }, '⏸️ 停止')
        ]),
        h('div', {
          style: {
            background: 'white',
            borderRadius: '10px',
            padding: '20px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
          }
        }, [
          h('h3', '网格交易策略'),
          h('div', { style: { display: 'flex', justifyContent: 'space-between', margin: '10px 0' } }, [
            h('span', '状态:'),
            h('span', { style: { color: '#95a5a6', fontWeight: 'bold' } }, '已停止')
          ]),
          h('div', { style: { display: 'flex', justifyContent: 'space-between', margin: '10px 0' } }, [
            h('span', '总收益率:'),
            h('span', { style: { color: '#e74c3c' } }, '-2.1%')
          ]),
          h('button', {
            style: {
              padding: '8px 16px',
              background: '#27ae60',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }
          }, '▶️ 启动')
        ])
      ])
    ])
  }
}

// 创建登录页面组件
const Login = {
  setup() {
    const loginForm = ref({
      username: '',
      password: ''
    })
    const isLogin = ref(true)
    const loading = ref(false)

    const handleLogin = async () => {
      if (!loginForm.value.username || !loginForm.value.password) {
        alert('请填写用户名和密码')
        return
      }

      loading.value = true
      try {
        // 模拟登录API调用
        const response = await fetch('http://localhost:8000/api/v1/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(loginForm.value)
        })

        if (response.ok) {
          const data = await response.json()
          console.log('登录成功:', data)

          // 保存token到localStorage
          localStorage.setItem('token', data.token)
          localStorage.setItem('userInfo', JSON.stringify(data.user))

          alert('登录成功！')
          // 跳转到仪表板
          router.push({ name: 'dashboard' })
        } else {
          const error = await response.json()
          alert('登录失败: ' + (error.detail || '未知错误'))
        }
      } catch (error) {
        console.error('登录错误:', error)
        alert('登录失败: 网络错误')
      } finally {
        loading.value = false
      }
    }

    const handleDemoLogin = () => {
      loginForm.value.username = 'admin'
      loginForm.value.password = 'admin123'
      handleLogin()
    }

    const toggleMode = () => {
      isLogin.value = !isLogin.value
    }

    return {
      loginForm,
      isLogin,
      loading,
      handleLogin,
      handleDemoLogin,
      toggleMode
    }
  },

  render() {
    return h('div', {
      style: {
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }
    }, [
      h('div', {
        style: {
          background: 'white',
          borderRadius: '16px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
          width: '100%',
          maxWidth: '400px',
          padding: '40px'
        }
      }, [
        // 标题
        h('div', {
          style: {
            textAlign: 'center',
            marginBottom: '30px'
          }
        }, [
          h('h1', {
            style: {
              color: '#2c3e50',
              marginBottom: '10px',
              fontSize: '28px'
            }
          }, '🚀 量化投资平台'),
          h('p', {
            style: {
              color: '#666',
              fontSize: '14px'
            }
          }, this.isLogin ? '登录您的账户' : '创建新账户')
        ]),

        // 表单
        h('form', {
          onSubmit: (e) => {
            e.preventDefault()
            this.handleLogin()
          }
        }, [
          // 用户名输入
          h('div', {
            style: { marginBottom: '20px' }
          }, [
            h('label', {
              style: {
                display: 'block',
                marginBottom: '8px',
                fontWeight: '500',
                color: '#2c3e50'
              }
            }, '用户名'),
            h('input', {
              type: 'text',
              placeholder: '请输入用户名',
              value: this.loginForm.username,
              onInput: (e) => this.loginForm.username = e.target.value,
              style: {
                width: '100%',
                padding: '12px 16px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.3s ease'
              }
            })
          ]),

          // 密码输入
          h('div', {
            style: { marginBottom: '20px' }
          }, [
            h('label', {
              style: {
                display: 'block',
                marginBottom: '8px',
                fontWeight: '500',
                color: '#2c3e50'
              }
            }, '密码'),
            h('input', {
              type: 'password',
              placeholder: '请输入密码',
              value: this.loginForm.password,
              onInput: (e) => this.loginForm.password = e.target.value,
              style: {
                width: '100%',
                padding: '12px 16px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.3s ease'
              }
            })
          ]),

          // 登录按钮
          h('button', {
            type: 'submit',
            disabled: this.loading,
            style: {
              width: '100%',
              padding: '12px',
              background: this.loading ? '#95a5a6' : '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: this.loading ? 'not-allowed' : 'pointer',
              marginBottom: '15px',
              transition: 'background 0.3s ease'
            }
          }, this.loading ? '登录中...' : (this.isLogin ? '登录' : '注册')),

          // 演示登录按钮
          this.isLogin ? h('button', {
            type: 'button',
            onClick: this.handleDemoLogin,
            disabled: this.loading,
            style: {
              width: '100%',
              padding: '12px',
              background: this.loading ? '#95a5a6' : '#27ae60',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              cursor: this.loading ? 'not-allowed' : 'pointer',
              marginBottom: '20px',
              transition: 'background 0.3s ease'
            }
          }, '🎯 演示登录 (admin/admin123)') : null,

          // 切换模式
          h('div', {
            style: {
              textAlign: 'center',
              fontSize: '14px',
              color: '#666'
            }
          }, [
            h('span', this.isLogin ? '还没有账户？' : '已有账户？'),
            h('button', {
              type: 'button',
              onClick: this.toggleMode,
              style: {
                background: 'none',
                border: 'none',
                color: '#3498db',
                cursor: 'pointer',
                textDecoration: 'underline',
                marginLeft: '5px'
              }
            }, this.isLogin ? '立即注册' : '立即登录')
          ])
        ])
      ])
    ])
  }
}

// 创建路由配置
const routes = [
  { path: '/login', component: Login, name: 'login', meta: { requiresAuth: false } },
  { path: '/', component: Dashboard, name: 'dashboard' },
  { path: '/market', component: Market, name: 'market' },
  { path: '/trading', component: Trading, name: 'trading' },
  { path: '/strategy', component: Strategy, name: 'strategy' }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 主应用组件 - 包含导航和路由视图
const MainApp = {
  setup() {
    const currentTime = ref('')
    const backendStatus = ref(null)
    const currentRoute = ref('dashboard')
    const isLoggedIn = ref(false)
    const userInfo = ref(null)

    const updateTime = () => {
      currentTime.value = new Date().toLocaleString('zh-CN')
    }

    const testBackend = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/health')
        if (response.ok) {
          const data = await response.json()
          backendStatus.value = {
            success: true,
            message: `连接正常 - ${data.status}`
          }
        } else {
          backendStatus.value = {
            success: false,
            message: `连接失败 - ${response.status}`
          }
        }
      } catch (error) {
        backendStatus.value = {
          success: false,
          message: `连接错误 - ${error.message}`
        }
      }
    }

    const checkLoginStatus = () => {
      const token = localStorage.getItem('token')
      const storedUserInfo = localStorage.getItem('userInfo')

      if (token && storedUserInfo) {
        try {
          userInfo.value = JSON.parse(storedUserInfo)
          isLoggedIn.value = true
        } catch (error) {
          console.error('解析用户信息失败:', error)
          logout()
        }
      }
    }

    const navigateTo = (routeName: string) => {
      // 检查是否需要登录
      if (!isLoggedIn.value && routeName !== 'login') {
        router.push({ name: 'login' })
        return
      }

      currentRoute.value = routeName
      router.push({ name: routeName })
    }

    const logout = () => {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      isLoggedIn.value = false
      userInfo.value = null
      router.push({ name: 'login' })
    }

    const goToLogin = () => {
      router.push({ name: 'login' })
    }

    onMounted(() => {
      updateTime()
      setInterval(updateTime, 1000)
      testBackend()
      checkLoginStatus()

      // 监听路由变化
      router.afterEach((to) => {
        currentRoute.value = to.name
      })

      console.log('✅ 带路由的量化投资平台已挂载')
    })

    return {
      currentTime,
      backendStatus,
      currentRoute,
      isLoggedIn,
      userInfo,
      testBackend,
      navigateTo,
      logout,
      goToLogin
    }
  },

  render() {
    return h('div', {
      style: {
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }
    }, [
      // 顶部导航栏
      h('nav', {
        style: {
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          padding: '15px 0',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          position: 'sticky',
          top: '0',
          zIndex: '1000'
        }
      }, [
        h('div', {
          style: {
            maxWidth: '1200px',
            margin: '0 auto',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '0 20px'
          }
        }, [
          // Logo和标题
          h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '15px'
            }
          }, [
            h('h1', {
              style: {
                color: '#2c3e50',
                margin: '0',
                fontSize: '24px'
              }
            }, '🚀 量化投资平台'),
            h('span', {
              style: {
                color: '#666',
                fontSize: '14px'
              }
            }, this.currentTime)
          ]),

          // 导航菜单 - 只在登录时显示
          this.isLoggedIn ? h('div', {
            style: {
              display: 'flex',
              gap: '20px'
            }
          }, [
            h('button', {
              onClick: () => this.navigateTo('dashboard'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'dashboard' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'dashboard' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '📊 仪表板'),
            h('button', {
              onClick: () => this.navigateTo('market'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'market' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'market' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '📈 市场数据'),
            h('button', {
              onClick: () => this.navigateTo('trading'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'trading' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'trading' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '💼 交易中心'),
            h('button', {
              onClick: () => this.navigateTo('strategy'),
              style: {
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                background: this.currentRoute === 'strategy' ? '#3498db' : 'transparent',
                color: this.currentRoute === 'strategy' ? 'white' : '#2c3e50',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.3s ease'
              }
            }, '🤖 策略管理')
          ]) : h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '15px'
            }
          }, [
            h('span', {
              style: {
                color: '#666',
                fontSize: '14px'
              }
            }, '请登录以访问完整功能'),
            h('button', {
              onClick: this.goToLogin,
              style: {
                padding: '8px 16px',
                background: '#3498db',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }
            }, '登录')
          ]),

          // 状态信息和用户信息
          h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '15px'
            }
          }, [
            // 后端连接状态
            this.backendStatus ? h('div', {
              style: {
                padding: '5px 10px',
                borderRadius: '15px',
                fontSize: '12px',
                fontWeight: 'bold',
                background: this.backendStatus.success ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
                color: this.backendStatus.success ? '#4CAF50' : '#f44336'
              }
            }, this.backendStatus.success ? '🟢 在线' : '🔴 离线') : null,

            // 用户信息 - 只在登录时显示
            this.isLoggedIn && this.userInfo ? h('div', {
              style: {
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '5px 10px',
                background: 'rgba(52, 152, 219, 0.1)',
                borderRadius: '15px'
              }
            }, [
              h('span', {
                style: {
                  fontSize: '12px',
                  color: '#3498db',
                  fontWeight: 'bold'
                }
              }, `👤 ${this.userInfo.username}`)
            ]) : null,

            // 操作按钮
            this.isLoggedIn ? h('button', {
              onClick: this.logout,
              style: {
                padding: '8px 15px',
                border: '1px solid #e74c3c',
                borderRadius: '6px',
                background: 'white',
                color: '#e74c3c',
                cursor: 'pointer',
                fontSize: '12px'
              }
            }, '🚪 登出') : null,

            h('button', {
              onClick: this.testBackend,
              style: {
                padding: '8px 15px',
                border: '1px solid #ddd',
                borderRadius: '6px',
                background: 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }
            }, '🔄 刷新')
          ])
        ])
      ]),

      // 主内容区域
      h('main', {
        style: {
          minHeight: 'calc(100vh - 80px)',
          background: '#f8f9fa'
        }
      }, [
        // 路由视图
        h('router-view')
      ])
    ])
  }
}

// 创建应用
const pinia = createPinia()
const app = createApp(MainApp)

// 配置应用
app.use(pinia)
app.use(router)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue错误:', err, info)
}

// 挂载应用
app.mount('#app')

console.log('🔧 带路由的量化投资平台已启动')
