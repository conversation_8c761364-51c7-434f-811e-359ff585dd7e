import { createApp, ref, onMounted, h } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'

console.log('🚀 启动完整版量化投资平台...')

// 导入页面组件
import Dashboard from './views/Dashboard.vue'
import Market from './views/Market.vue'
import Trading from './views/Trading.vue'
import Strategy from './views/Strategy.vue'

// 创建路由配置
const routes = [
  { path: '/', component: Dashboard, name: 'dashboard', meta: { title: '仪表板' } },
  { path: '/market', component: Market, name: 'market', meta: { title: '市场数据' } },
  { path: '/trading', component: Trading, name: 'trading', meta: { title: '交易中心' } },
  { path: '/strategy', component: Strategy, name: 'strategy', meta: { title: '策略管理' } }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 主应用组件 - 包含导航和路由视图
const MainApp = {
  setup() {
    const currentTime = ref('')
    const backendStatus = ref(null)
    const currentRoute = ref('dashboard')

    const updateTime = () => {
      currentTime.value = new Date().toLocaleString('zh-CN')
    }

    const testBackend = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/health')
        if (response.ok) {
          const data = await response.json()
          backendStatus.value = {
            success: true,
            message: `连接正常 - ${data.status}`
          }
        } else {
          backendStatus.value = {
            success: false,
            message: `连接失败 - ${response.status}`
          }
        }
      } catch (error) {
        backendStatus.value = {
          success: false,
          message: `连接错误 - ${error.message}`
        }
      }
    }

    const navigateTo = (routeName: string) => {
      currentRoute.value = routeName
      router.push({ name: routeName })
    }

    onMounted(() => {
      updateTime()
      setInterval(updateTime, 1000)
      testBackend()
      console.log('✅ 完整版量化投资平台已挂载')
    })

    return {
      currentTime,
      backendStatus,
      currentRoute,
      testBackend,
      navigateTo
    }
  },
  render() {
    return h('div', {
      style: {
        padding: '20px',
        fontFamily: 'Arial, sans-serif',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        minHeight: '100vh',
        textAlign: 'center'
      }
    }, [
      h('h1', { style: { color: '#fff', marginBottom: '20px' } }, '🎉 Vue 3 Composition API 测试'),
      h('div', {
        style: {
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '30px',
          borderRadius: '15px',
          backdropFilter: 'blur(10px)',
          maxWidth: '600px',
          margin: '20px auto'
        }
      }, [
        h('p', { style: { fontSize: '18px', marginBottom: '10px' } }, [
          h('strong', '当前时间: '),
          this.currentTime
        ]),
        h('p', { style: { fontSize: '18px', marginBottom: '20px' } }, [
          h('strong', '应用状态: '),
          '已成功挂载并运行'
        ]),
        h('div', { style: { marginBottom: '20px' } }, [
          h('button', {
            onClick: () => this.count++,
            style: {
              padding: '10px 20px',
              background: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              margin: '5px',
              fontSize: '16px'
            }
          }, `点击测试 (${this.count})`),
          h('button', {
            onClick: this.testBackend,
            style: {
              padding: '10px 20px',
              background: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              margin: '5px',
              fontSize: '16px'
            }
          }, '测试后端连接'),
          h('button', {
            onClick: () => window.location.reload(),
            style: {
              padding: '10px 20px',
              background: '#FF9800',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              margin: '5px',
              fontSize: '16px'
            }
          }, '重新加载')
        ]),
        this.backendStatus ? h('div', {
          style: {
            margin: '20px 0',
            padding: '15px',
            borderRadius: '5px',
            background: this.backendStatus.success ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)',
            border: `1px solid ${this.backendStatus.success ? '#4CAF50' : '#f44336'}`
          }
        }, [
          h('strong', '后端状态: '),
          this.backendStatus.message
        ]) : null,
        h('div', {
          style: {
            marginTop: '30px',
            fontSize: '14px',
            opacity: '0.8'
          }
        }, [
          h('p', '✅ Vue 3 Composition API 正常工作'),
          h('p', '✅ 响应式数据绑定正常'),
          h('p', '✅ 事件处理正常'),
          h('p', '✅ 生命周期钩子正常')
        ])
      ])
    ])
  }
}

try {
  console.log('创建Vue应用实例...')
  const app = createApp(SimpleApp)

  // 全局错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('Vue错误:', err, info)
  }

  console.log('挂载应用到#app...')
  app.mount('#app')

  console.log('✅ Composition API应用启动成功!')

} catch (error) {
  console.error('❌ 应用启动失败:', error)

  // 显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; background: #ffebee; color: #c62828; text-align: center; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
        <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px;">
          <h1>❌ 应用启动失败</h1>
          <p><strong>错误:</strong> ${error.message}</p>
          <button onclick="location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
        </div>
      </div>
    `
  }
}
