<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>📊 仪表板</h1>
      <p>量化投资平台总览</p>
    </div>
    
    <div class="dashboard-grid">
      <div class="card">
        <h3>💰 账户总览</h3>
        <div class="stats">
          <div class="stat-item">
            <span class="label">总资产</span>
            <span class="value">¥{{ formatCurrency(totalAssets) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">可用资金</span>
            <span class="value">¥{{ formatCurrency(availableFunds) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">今日盈亏</span>
            <span class="value" :class="{ 'positive': todayPnL > 0, 'negative': todayPnL < 0 }">
              {{ todayPnL > 0 ? '+' : '' }}¥{{ formatCurrency(Math.abs(todayPnL)) }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="card">
        <h3>📈 市场概览</h3>
        <div class="market-stats">
          <div class="market-item">
            <span class="index-name">上证指数</span>
            <span class="index-value">3,245.67</span>
            <span class="index-change positive">+1.23%</span>
          </div>
          <div class="market-item">
            <span class="index-name">深证成指</span>
            <span class="index-value">10,876.54</span>
            <span class="index-change negative">-0.45%</span>
          </div>
          <div class="market-item">
            <span class="index-name">创业板指</span>
            <span class="index-value">2,156.89</span>
            <span class="index-change positive">+0.78%</span>
          </div>
        </div>
      </div>
      
      <div class="card">
        <h3>🤖 策略状态</h3>
        <div class="strategy-stats">
          <div class="strategy-item">
            <span class="strategy-name">量化策略A</span>
            <span class="strategy-status running">运行中</span>
          </div>
          <div class="strategy-item">
            <span class="strategy-name">套利策略B</span>
            <span class="strategy-status stopped">已停止</span>
          </div>
          <div class="strategy-item">
            <span class="strategy-name">趋势策略C</span>
            <span class="strategy-status running">运行中</span>
          </div>
        </div>
      </div>
      
      <div class="card">
        <h3>⚡ 快速操作</h3>
        <div class="quick-actions">
          <button class="action-btn primary" @click="goToMarket">查看市场</button>
          <button class="action-btn secondary" @click="goToTrading">开始交易</button>
          <button class="action-btn tertiary" @click="goToStrategy">策略管理</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const totalAssets = ref(1000000)
const availableFunds = ref(250000)
const todayPnL = ref(12500)

// 格式化货币
const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

// 导航方法
const goToMarket = () => router.push('/market')
const goToTrading = () => router.push('/trading')
const goToStrategy = () => router.push('/strategy')

onMounted(() => {
  console.log('📊 仪表板页面已加载')
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.card h3 {
  margin-bottom: 15px;
  color: #2c3e50;
}

.stats, .market-stats, .strategy-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item, .market-item, .strategy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child, .market-item:last-child, .strategy-item:last-child {
  border-bottom: none;
}

.value {
  font-weight: bold;
  font-size: 16px;
}

.positive {
  color: #27ae60;
}

.negative {
  color: #e74c3c;
}

.strategy-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.strategy-status.running {
  background: #d4edda;
  color: #155724;
}

.strategy-status.stopped {
  background: #f8d7da;
  color: #721c24;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #3498db;
  color: white;
}

.action-btn.secondary {
  background: #2ecc71;
  color: white;
}

.action-btn.tertiary {
  background: #f39c12;
  color: white;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
</style>
