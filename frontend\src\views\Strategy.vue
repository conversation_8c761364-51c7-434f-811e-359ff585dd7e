<template>
  <div class="strategy">
    <div class="strategy-header">
      <h1>🤖 策略管理</h1>
      <p>量化策略开发与管理</p>
    </div>
    
    <div class="strategy-content">
      <div class="strategy-actions">
        <button class="btn-primary" @click="createStrategy">+ 创建策略</button>
        <button class="btn-secondary" @click="importStrategy">📥 导入策略</button>
        <button class="btn-tertiary" @click="exportStrategies">📤 导出策略</button>
      </div>
      
      <div class="strategies-grid">
        <div class="strategy-card" v-for="strategy in strategies" :key="strategy.id">
          <div class="strategy-header-card">
            <h3>{{ strategy.name }}</h3>
            <div class="strategy-status" :class="strategy.status">
              {{ getStatusText(strategy.status) }}
            </div>
          </div>
          
          <div class="strategy-info">
            <div class="info-item">
              <span class="label">策略类型:</span>
              <span class="value">{{ strategy.type }}</span>
            </div>
            <div class="info-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ strategy.createTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">运行时长:</span>
              <span class="value">{{ strategy.runTime }}</span>
            </div>
          </div>
          
          <div class="strategy-performance">
            <div class="perf-item">
              <span class="label">总收益率</span>
              <span class="value" :class="{ 'positive': strategy.totalReturn > 0, 'negative': strategy.totalReturn < 0 }">
                {{ strategy.totalReturn > 0 ? '+' : '' }}{{ strategy.totalReturn }}%
              </span>
            </div>
            <div class="perf-item">
              <span class="label">年化收益</span>
              <span class="value" :class="{ 'positive': strategy.annualReturn > 0, 'negative': strategy.annualReturn < 0 }">
                {{ strategy.annualReturn > 0 ? '+' : '' }}{{ strategy.annualReturn }}%
              </span>
            </div>
            <div class="perf-item">
              <span class="label">最大回撤</span>
              <span class="value negative">{{ strategy.maxDrawdown }}%</span>
            </div>
            <div class="perf-item">
              <span class="label">夏普比率</span>
              <span class="value">{{ strategy.sharpeRatio }}</span>
            </div>
          </div>
          
          <div class="strategy-actions-card">
            <button v-if="strategy.status === 'stopped'" class="btn-start" @click="startStrategy(strategy.id)">
              ▶️ 启动
            </button>
            <button v-if="strategy.status === 'running'" class="btn-stop" @click="stopStrategy(strategy.id)">
              ⏸️ 停止
            </button>
            <button class="btn-edit" @click="editStrategy(strategy.id)">
              ✏️ 编辑
            </button>
            <button class="btn-backtest" @click="backtestStrategy(strategy.id)">
              📊 回测
            </button>
            <button class="btn-delete" @click="deleteStrategy(strategy.id)">
              🗑️ 删除
            </button>
          </div>
        </div>
      </div>
      
      <div class="strategy-logs">
        <h3>📝 策略日志</h3>
        <div class="logs-container">
          <div class="log-item" v-for="log in strategyLogs" :key="log.id">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-level" :class="log.level">{{ log.level.toUpperCase() }}</span>
            <span class="log-strategy">{{ log.strategy }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 策略列表
const strategies = ref([
  {
    id: 'strategy_001',
    name: '均线突破策略',
    type: '趋势跟踪',
    status: 'running',
    createTime: '2025-01-10',
    runTime: '15天',
    totalReturn: 8.5,
    annualReturn: 12.3,
    maxDrawdown: -3.2,
    sharpeRatio: 1.45
  },
  {
    id: 'strategy_002',
    name: '网格交易策略',
    type: '套利策略',
    status: 'stopped',
    createTime: '2025-01-05',
    runTime: '20天',
    totalReturn: -2.1,
    annualReturn: -5.8,
    maxDrawdown: -8.5,
    sharpeRatio: 0.32
  },
  {
    id: 'strategy_003',
    name: '动量反转策略',
    type: '反转策略',
    status: 'running',
    createTime: '2025-01-15',
    runTime: '10天',
    totalReturn: 15.2,
    annualReturn: 28.7,
    maxDrawdown: -5.1,
    sharpeRatio: 2.18
  }
])

// 策略日志
const strategyLogs = ref([
  { id: 1, time: '17:05:30', level: 'info', strategy: '均线突破策略', message: '买入信号触发，股票代码: 000001' },
  { id: 2, time: '17:03:15', level: 'warn', strategy: '网格交易策略', message: '价格超出网格范围，暂停交易' },
  { id: 3, time: '17:01:45', level: 'info', strategy: '动量反转策略', message: '卖出信号触发，股票代码: 600036' },
  { id: 4, time: '16:58:20', level: 'error', strategy: '均线突破策略', message: '网络连接异常，重试中...' },
  { id: 5, time: '16:55:10', level: 'info', strategy: '动量反转策略', message: '策略参数更新完成' }
])

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '错误'
  }
  return statusMap[status] || status
}

// 策略操作方法
const createStrategy = () => {
  console.log('🤖 创建新策略')
  // 这里可以打开策略创建对话框
}

const importStrategy = () => {
  console.log('📥 导入策略')
  // 这里可以打开文件选择对话框
}

const exportStrategies = () => {
  console.log('📤 导出策略')
  // 这里可以导出策略配置
}

const startStrategy = (strategyId: string) => {
  console.log('▶️ 启动策略:', strategyId)
  const strategy = strategies.value.find(s => s.id === strategyId)
  if (strategy) {
    strategy.status = 'running'
  }
}

const stopStrategy = (strategyId: string) => {
  console.log('⏸️ 停止策略:', strategyId)
  const strategy = strategies.value.find(s => s.id === strategyId)
  if (strategy) {
    strategy.status = 'stopped'
  }
}

const editStrategy = (strategyId: string) => {
  console.log('✏️ 编辑策略:', strategyId)
  // 这里可以打开策略编辑器
}

const backtestStrategy = (strategyId: string) => {
  console.log('📊 回测策略:', strategyId)
  // 这里可以跳转到回测页面
}

const deleteStrategy = (strategyId: string) => {
  console.log('🗑️ 删除策略:', strategyId)
  if (confirm('确定要删除这个策略吗？')) {
    const index = strategies.value.findIndex(s => s.id === strategyId)
    if (index > -1) {
      strategies.value.splice(index, 1)
    }
  }
}

onMounted(() => {
  console.log('🤖 策略管理页面已加载')
})
</script>

<style scoped>
.strategy {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.strategy-header {
  text-align: center;
  margin-bottom: 30px;
}

.strategy-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.strategy-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.strategy-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn-primary, .btn-secondary, .btn-tertiary {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-secondary {
  background: #2ecc71;
  color: white;
}

.btn-tertiary {
  background: #f39c12;
  color: white;
}

.strategies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.strategy-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.strategy-header-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.strategy-header-card h3 {
  color: #2c3e50;
  margin: 0;
}

.strategy-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.strategy-status.running {
  background: #d4edda;
  color: #155724;
}

.strategy-status.stopped {
  background: #f8d7da;
  color: #721c24;
}

.strategy-info, .strategy-performance {
  margin-bottom: 15px;
}

.info-item, .perf-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  font-weight: 500;
}

.positive {
  color: #27ae60;
}

.negative {
  color: #e74c3c;
}

.strategy-actions-card {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-start, .btn-stop, .btn-edit, .btn-backtest, .btn-delete {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-start {
  background: #27ae60;
  color: white;
}

.btn-stop {
  background: #e74c3c;
  color: white;
}

.btn-edit {
  background: #3498db;
  color: white;
}

.btn-backtest {
  background: #9b59b6;
  color: white;
}

.btn-delete {
  background: #95a5a6;
  color: white;
}

.strategy-logs {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e1e8ed;
  border-radius: 5px;
  padding: 10px;
  background: #f8f9fa;
}

.log-item {
  display: grid;
  grid-template-columns: auto auto 1fr 2fr;
  gap: 15px;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
}

.log-level {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
}

.log-level.info {
  background: #d1ecf1;
  color: #0c5460;
}

.log-level.warn {
  background: #fff3cd;
  color: #856404;
}

.log-level.error {
  background: #f8d7da;
  color: #721c24;
}

.log-strategy {
  color: #495057;
  font-weight: 500;
}

.log-message {
  color: #212529;
}
</style>
