<template>
  <div class="market">
    <div class="market-header">
      <h1>📈 市场数据</h1>
      <p>实时行情与市场分析</p>
    </div>
    
    <div class="market-content">
      <div class="market-overview">
        <h3>市场概览</h3>
        <div class="indices-grid">
          <div class="index-card" v-for="index in marketIndices" :key="index.code">
            <div class="index-name">{{ index.name }}</div>
            <div class="index-value">{{ index.value }}</div>
            <div class="index-change" :class="{ 'positive': index.change > 0, 'negative': index.change < 0 }">
              {{ index.change > 0 ? '+' : '' }}{{ index.change }}%
            </div>
          </div>
        </div>
      </div>
      
      <div class="stock-list">
        <h3>热门股票</h3>
        <div class="stock-table">
          <div class="table-header">
            <span>股票代码</span>
            <span>股票名称</span>
            <span>当前价格</span>
            <span>涨跌幅</span>
            <span>成交量</span>
          </div>
          <div class="table-row" v-for="stock in hotStocks" :key="stock.code">
            <span class="stock-code">{{ stock.code }}</span>
            <span class="stock-name">{{ stock.name }}</span>
            <span class="stock-price">¥{{ stock.price }}</span>
            <span class="stock-change" :class="{ 'positive': stock.change > 0, 'negative': stock.change < 0 }">
              {{ stock.change > 0 ? '+' : '' }}{{ stock.change }}%
            </span>
            <span class="stock-volume">{{ formatVolume(stock.volume) }}</span>
          </div>
        </div>
      </div>
      
      <div class="market-tools">
        <h3>市场工具</h3>
        <div class="tools-grid">
          <button class="tool-btn" @click="refreshData">
            🔄 刷新数据
          </button>
          <button class="tool-btn" @click="showChart">
            📊 查看图表
          </button>
          <button class="tool-btn" @click="exportData">
            📥 导出数据
          </button>
          <button class="tool-btn" @click="setAlert">
            🔔 设置提醒
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 市场指数数据
const marketIndices = ref([
  { code: 'SH000001', name: '上证指数', value: '3,245.67', change: 1.23 },
  { code: 'SZ399001', name: '深证成指', value: '10,876.54', change: -0.45 },
  { code: 'SZ399006', name: '创业板指', value: '2,156.89', change: 0.78 },
  { code: 'SZ399905', name: '中证500', value: '5,432.10', change: 0.34 }
])

// 热门股票数据
const hotStocks = ref([
  { code: '000001', name: '平安银行', price: 12.45, change: 2.1, volume: 125000000 },
  { code: '000002', name: '万科A', price: 18.76, change: -1.2, volume: 89000000 },
  { code: '600036', name: '招商银行', price: 42.33, change: 1.8, volume: 156000000 },
  { code: '600519', name: '贵州茅台', price: 1678.90, change: 0.5, volume: 23000000 },
  { code: '000858', name: '五粮液', price: 156.78, change: -0.8, volume: 67000000 }
])

// 格式化成交量
const formatVolume = (volume: number) => {
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(1) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  }
  return volume.toString()
}

// 工具方法
const refreshData = () => {
  console.log('🔄 刷新市场数据')
  // 这里可以调用API刷新数据
}

const showChart = () => {
  console.log('📊 显示图表')
  // 这里可以打开图表组件
}

const exportData = () => {
  console.log('📥 导出数据')
  // 这里可以导出市场数据
}

const setAlert = () => {
  console.log('🔔 设置价格提醒')
  // 这里可以设置价格提醒
}

onMounted(() => {
  console.log('📈 市场数据页面已加载')
  refreshData()
})
</script>

<style scoped>
.market {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.market-header {
  text-align: center;
  margin-bottom: 30px;
}

.market-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.market-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.market-overview, .stock-list, .market-tools {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.indices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.index-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.index-name {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.index-value {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.index-change {
  font-size: 14px;
  font-weight: bold;
}

.stock-table {
  margin-top: 15px;
}

.table-header, .table-row {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e1e8ed;
}

.table-header {
  font-weight: bold;
  background: #f8f9fa;
  padding: 15px 0;
  border-radius: 5px;
}

.table-row:hover {
  background: #f8f9fa;
}

.stock-code {
  color: #666;
  font-family: monospace;
}

.stock-name {
  font-weight: 500;
}

.stock-price {
  font-weight: bold;
}

.positive {
  color: #27ae60;
}

.negative {
  color: #e74c3c;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.tool-btn {
  padding: 12px 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #f8f9fa;
  border-color: #3498db;
  transform: translateY(-2px);
}
</style>
