/**
 * 量化投资平台主入口文件
 */
import { createApp } from 'vue'
import App from './AppFixed.vue'

console.log('🚀 启动量化投资平台...')

// 错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason)
})

try {
  console.log('创建Vue应用...')
  const app = createApp(App)

  console.log('挂载应用到 #app...')
  app.mount('#app')

  console.log('✅ 量化投资平台启动成功!')

} catch (error: any) {
  console.error('❌ 应用启动失败:', error)

  // 显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; background: #ffebee; color: #c62828; text-align: center; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
        <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px;">
          <h1>❌ 应用启动失败</h1>
          <p><strong>错误:</strong> ${error.message}</p>
          <button onclick="location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
        </div>
      </div>
    `
  }
}
