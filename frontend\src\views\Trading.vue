<template>
  <div class="trading">
    <div class="trading-header">
      <h1>💼 交易中心</h1>
      <p>股票交易与订单管理</p>
    </div>
    
    <div class="trading-content">
      <div class="trading-panel">
        <h3>📝 下单交易</h3>
        <div class="order-form">
          <div class="form-group">
            <label>股票代码</label>
            <input v-model="orderForm.stockCode" type="text" placeholder="请输入股票代码" />
          </div>
          <div class="form-group">
            <label>股票名称</label>
            <input v-model="orderForm.stockName" type="text" readonly />
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>交易类型</label>
              <select v-model="orderForm.orderType">
                <option value="buy">买入</option>
                <option value="sell">卖出</option>
              </select>
            </div>
            <div class="form-group">
              <label>价格类型</label>
              <select v-model="orderForm.priceType">
                <option value="market">市价</option>
                <option value="limit">限价</option>
              </select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>交易价格</label>
              <input v-model="orderForm.price" type="number" step="0.01" :disabled="orderForm.priceType === 'market'" />
            </div>
            <div class="form-group">
              <label>交易数量</label>
              <input v-model="orderForm.quantity" type="number" step="100" />
            </div>
          </div>
          <div class="form-actions">
            <button class="btn-submit" :class="orderForm.orderType" @click="submitOrder">
              {{ orderForm.orderType === 'buy' ? '买入' : '卖出' }}
            </button>
            <button class="btn-reset" @click="resetForm">重置</button>
          </div>
        </div>
      </div>
      
      <div class="positions-panel">
        <h3>📊 持仓信息</h3>
        <div class="positions-table">
          <div class="table-header">
            <span>股票代码</span>
            <span>股票名称</span>
            <span>持仓数量</span>
            <span>成本价</span>
            <span>当前价</span>
            <span>盈亏</span>
            <span>操作</span>
          </div>
          <div class="table-row" v-for="position in positions" :key="position.code">
            <span class="stock-code">{{ position.code }}</span>
            <span class="stock-name">{{ position.name }}</span>
            <span class="quantity">{{ position.quantity }}</span>
            <span class="cost-price">¥{{ position.costPrice }}</span>
            <span class="current-price">¥{{ position.currentPrice }}</span>
            <span class="pnl" :class="{ 'positive': position.pnl > 0, 'negative': position.pnl < 0 }">
              {{ position.pnl > 0 ? '+' : '' }}¥{{ Math.abs(position.pnl).toFixed(2) }}
            </span>
            <span class="actions">
              <button class="btn-sell" @click="sellPosition(position)">卖出</button>
            </span>
          </div>
        </div>
      </div>
      
      <div class="orders-panel">
        <h3>📋 委托订单</h3>
        <div class="orders-table">
          <div class="table-header">
            <span>订单号</span>
            <span>股票代码</span>
            <span>类型</span>
            <span>价格</span>
            <span>数量</span>
            <span>状态</span>
            <span>时间</span>
            <span>操作</span>
          </div>
          <div class="table-row" v-for="order in orders" :key="order.id">
            <span class="order-id">{{ order.id }}</span>
            <span class="stock-code">{{ order.stockCode }}</span>
            <span class="order-type" :class="order.type">{{ order.type === 'buy' ? '买入' : '卖出' }}</span>
            <span class="price">¥{{ order.price }}</span>
            <span class="quantity">{{ order.quantity }}</span>
            <span class="status" :class="order.status">{{ getStatusText(order.status) }}</span>
            <span class="time">{{ order.time }}</span>
            <span class="actions">
              <button v-if="order.status === 'pending'" class="btn-cancel" @click="cancelOrder(order.id)">撤单</button>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

// 订单表单
const orderForm = ref({
  stockCode: '',
  stockName: '',
  orderType: 'buy',
  priceType: 'limit',
  price: 0,
  quantity: 100
})

// 持仓数据
const positions = ref([
  { code: '000001', name: '平安银行', quantity: 1000, costPrice: 12.00, currentPrice: 12.45, pnl: 450 },
  { code: '600036', name: '招商银行', quantity: 500, costPrice: 41.50, currentPrice: 42.33, pnl: 415 },
  { code: '000858', name: '五粮液', quantity: 200, costPrice: 158.00, currentPrice: 156.78, pnl: -244 }
])

// 委托订单
const orders = ref([
  { id: 'T20250814001', stockCode: '600519', type: 'buy', price: 1650.00, quantity: 10, status: 'pending', time: '09:30:15' },
  { id: 'T20250814002', stockCode: '000002', type: 'sell', price: 19.00, quantity: 500, status: 'filled', time: '10:15:32' },
  { id: 'T20250814003', stockCode: '600036', type: 'buy', price: 42.00, quantity: 300, status: 'cancelled', time: '11:20:45' }
])

// 监听股票代码变化，自动填充股票名称
watch(() => orderForm.value.stockCode, (newCode) => {
  const stockNames: Record<string, string> = {
    '000001': '平安银行',
    '000002': '万科A',
    '600036': '招商银行',
    '600519': '贵州茅台',
    '000858': '五粮液'
  }
  orderForm.value.stockName = stockNames[newCode] || ''
})

// 提交订单
const submitOrder = () => {
  if (!orderForm.value.stockCode || !orderForm.value.quantity) {
    alert('请填写完整的订单信息')
    return
  }
  
  console.log('📝 提交订单:', orderForm.value)
  
  // 模拟添加到订单列表
  const newOrder = {
    id: `T${Date.now()}`,
    stockCode: orderForm.value.stockCode,
    type: orderForm.value.orderType,
    price: orderForm.value.priceType === 'market' ? 0 : orderForm.value.price,
    quantity: orderForm.value.quantity,
    status: 'pending',
    time: new Date().toLocaleTimeString()
  }
  
  orders.value.unshift(newOrder)
  alert('订单提交成功！')
}

// 重置表单
const resetForm = () => {
  orderForm.value = {
    stockCode: '',
    stockName: '',
    orderType: 'buy',
    priceType: 'limit',
    price: 0,
    quantity: 100
  }
}

// 卖出持仓
const sellPosition = (position: any) => {
  console.log('💰 卖出持仓:', position)
  orderForm.value.stockCode = position.code
  orderForm.value.stockName = position.name
  orderForm.value.orderType = 'sell'
  orderForm.value.price = position.currentPrice
  orderForm.value.quantity = position.quantity
}

// 撤销订单
const cancelOrder = (orderId: string) => {
  const order = orders.value.find(o => o.id === orderId)
  if (order) {
    order.status = 'cancelled'
    console.log('❌ 撤销订单:', orderId)
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待成交',
    'filled': '已成交',
    'cancelled': '已撤销'
  }
  return statusMap[status] || status
}

onMounted(() => {
  console.log('💼 交易中心页面已加载')
})
</script>

<style scoped>
.trading {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.trading-header {
  text-align: center;
  margin-bottom: 30px;
}

.trading-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.trading-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.trading-panel, .positions-panel, .orders-panel {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.order-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input, .form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.btn-submit, .btn-reset {
  padding: 12px 30px;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit.buy {
  background: #e74c3c;
  color: white;
}

.btn-submit.sell {
  background: #27ae60;
  color: white;
}

.btn-reset {
  background: #95a5a6;
  color: white;
}

.table-header, .table-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #e1e8ed;
}

.table-header {
  font-weight: bold;
  background: #f8f9fa;
  padding: 15px 0;
}

.table-row:hover {
  background: #f8f9fa;
}

.positive {
  color: #27ae60;
}

.negative {
  color: #e74c3c;
}

.order-type.buy {
  color: #e74c3c;
}

.order-type.sell {
  color: #27ae60;
}

.status.pending {
  color: #f39c12;
}

.status.filled {
  color: #27ae60;
}

.status.cancelled {
  color: #95a5a6;
}

.btn-sell, .btn-cancel {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.btn-sell {
  background: #27ae60;
  color: white;
}

.btn-cancel {
  background: #e74c3c;
  color: white;
}
</style>
